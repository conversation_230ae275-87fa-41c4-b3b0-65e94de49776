"""PR analysis service for orchestrating the security analysis pipeline."""

import uuid
from typing import Any, Dict, List, Optional

from platyfend_ai.analyzers import AstGrep<PERSON>nal<PERSON><PERSON>, <PERSON><PERSON><PERSON>nal<PERSON><PERSON>, SemgrepAnalyzer
from platyfend_ai.models.github import GitHubPRWebhookData
from platyfend_ai.services import <PERSON><PERSON><PERSON><PERSON><PERSON>
from platyfend_ai.services.comment_processing_service import CommentProcessingService
from platyfend_ai.services.idempotency_service import (
    IdempotencyService,
    mark_delivery_if_present,
)
from platyfend_ai.services.pr_validation_service import PRValidationService
from platyfend_ai.utils.error_handling import (
    AnalysisError,
    GitHubAPIError,
    PlatyfendError,
    get_user_friendly_message,
)
from platyfend_ai.utils.logging_config import get_logger

logger = get_logger(__name__)


class PRAnalysisService:
    """Service for orchestrating PR security analysis pipeline."""

    def __init__(
        self,
        validation_service: PRValidationService,
        idempotency_service: IdempotencyService,
        comment_processing_service: CommentProcessingService,
    ):
        self.validation_service = validation_service
        self.idempotency_service = idempotency_service
        self.comment_processing_service = comment_processing_service

    async def process_pr_webhook(
        self,
        pr_data: GitHubPRWebhookData,
        analysis_services,
        request_id: str = "",
        delivery_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Process PR security analysis pipeline using modular helper functions.

        Args:
            pr_data: GitHub PR webhook data
            analysis_services: Injected analysis services
            request_id: Unique request identifier for tracking
            delivery_id: Optional delivery ID for idempotency tracking

        Returns:
            Processing results
        """
        try:
            # Generate request ID if not provided
            if not request_id:
                request_id = str(uuid.uuid4())[:8]

            logger.info(
                f"[{request_id}] Starting security analysis pipeline for PR #{pr_data.number} in {pr_data.repository} (action: {pr_data.action}, sender: {pr_data.sender})"
            )

            # Validate if PR should be analyzed
            validation_result = await self.validation_service.validate_pr_for_analysis(
                pr_data, request_id
            )
            if validation_result:
                await mark_delivery_if_present(
                    self.idempotency_service, delivery_id, pr_data, "skipped"
                )
                return validation_result

            # Check analysis cache to prevent duplicate processing
            cache_result = await self.validation_service.check_analysis_cache(
                pr_data, request_id
            )
            if cache_result:
                await mark_delivery_if_present(
                    self.idempotency_service, delivery_id, pr_data, "skipped"
                )
                return cache_result

            # Fetch PR data (Step 1)
            try:
                pr_fetch_result = await self._fetch_pr_data(
                    pr_data, request_id, analysis_services.diff_fetcher
                )
                diff_content = pr_fetch_result["diff_content"]
                files_info = pr_fetch_result["files_info"]
            except GitHubAPIError as e:
                logger.error(f"[{request_id}] Failed to fetch PR data: {e}")
                await mark_delivery_if_present(
                    self.idempotency_service, delivery_id, pr_data, "error"
                )
                return {
                    "status": "error",
                    "error": "Failed to fetch PR diff",
                    "details": str(e),
                    "pr_number": pr_data.number,
                    "repository": pr_data.repository,
                }

            # Initialize analyzers (Step 2)
            registered_analyzers = await self._initialize_analyzers(
                request_id, analysis_services.analysis_engine, analysis_services.config
            )

            if not registered_analyzers:
                logger.warning("No analyzers available for analysis")
                return {
                    "status": "error",
                    "error": "No analyzers available",
                    "pr_number": pr_data.number,
                    "repository": pr_data.repository,
                }

            # Run security analysis (Step 3)
            analysis_result = await self._run_security_analysis(
                pr_data,
                diff_content,
                files_info,
                request_id,
                analysis_services.analysis_engine,
            )
            if "status" in analysis_result and analysis_result["status"] == "error":
                await mark_delivery_if_present(
                    self.idempotency_service, delivery_id, pr_data, "error"
                )
                return analysis_result

            analysis_report = analysis_result["analysis_report"]

            # Generate review comments (Step 4)
            review_comments = (
                await self.comment_processing_service.generate_review_comments(
                    analysis_report, pr_data, request_id
                )
            )

            # Send comments to frontend (Step 5)
            logger.info(
                f"[{request_id}] Step 5/5: Sending comments to Next.js frontend"
            )

            comment_results = {"sent_comments": 0, "errors": []}

            if review_comments:
                try:
                    comment_results = (
                        await self.comment_processing_service.send_comments_to_frontend(
                            pr_data=pr_data,
                            comments=review_comments,
                            analysis_report=analysis_report,
                        )
                    )
                    logger.info(
                        f"[{request_id}] Step 5/5 completed: Sent {comment_results['sent_comments']} comments to frontend"
                    )

                except Exception as e:
                    logger.error(
                        f"[{request_id}] Failed to send comments to frontend: {e}"
                    )
                    comment_results["errors"].append(str(e))
            else:
                logger.info(
                    f"[{request_id}] Step 5/5 completed: No review comments generated - skipped comment sending"
                )

            # Prepare response
            response_data = {
                "status": "success",
                "message": f"Security analysis completed for PR #{pr_data.number}",
                "pr_info": {
                    "id": pr_data.id,
                    "number": pr_data.number,
                    "title": pr_data.title,
                    "repository": pr_data.repository,
                    "action": pr_data.action,
                    "author": pr_data.author,
                    "base_branch": pr_data.base_branch,
                    "head_branch": pr_data.head_branch,
                    "draft": pr_data.draft,
                    "mergeable": pr_data.mergeable,
                    "stats": {
                        "commits": pr_data.commits,
                        "changed_files": pr_data.changed_files,
                        "additions": pr_data.additions,
                        "deletions": pr_data.deletions,
                    },
                },
                "analysis_results": {
                    "total_findings": analysis_report.total_findings,
                    "critical_findings": analysis_report.critical_findings,
                    "high_findings": analysis_report.high_findings,
                    "analyzers_run": len(analysis_report.analysis_results),
                    "success": analysis_report.success,
                    "errors": analysis_report.errors,
                },
                "comment_results": comment_results,
                "processing_time": (
                    (
                        analysis_report.completed_at - analysis_report.started_at
                    ).total_seconds()
                    if analysis_report.completed_at and analysis_report.started_at
                    else None
                ),
            }

            logger.info(
                f"[{request_id}] Successfully completed all 5 steps of security analysis pipeline for PR #{pr_data.number}"
            )

            # Mark delivery as successfully processed if we have a delivery ID
            await mark_delivery_if_present(
                self.idempotency_service, delivery_id, pr_data, "success"
            )

            return response_data

        except PlatyfendError as e:
            logger.error(f"Platyfend error in PR processing: {e}")

            # Mark delivery as failed if we have a delivery ID
            await mark_delivery_if_present(
                self.idempotency_service, delivery_id, pr_data, "error"
            )

            return {
                "status": "error",
                "error": get_user_friendly_message(e),
                "error_id": e.context.error_id if hasattr(e, "context") else None,
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }
        except Exception as e:
            logger.error(f"Unexpected error in PR processing: {e}", exc_info=True)

            # Mark delivery as failed if we have a delivery ID
            await mark_delivery_if_present(
                self.idempotency_service, delivery_id, pr_data, "error"
            )

            return {
                "status": "error",
                "error": "An unexpected error occurred during processing",
                "details": str(e),
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

    async def _fetch_pr_data(
        self, pr_data: GitHubPRWebhookData, request_id: str, diff_fetcher: DiffFetcher
    ) -> Dict[str, Any]:
        """
        Fetch PR diff and file information using injected service.

        Args:
            pr_data: GitHub PR webhook data
            request_id: Request ID for logging
            diff_fetcher: Injected DiffFetcher service

        Returns:
            Dict containing diff_content and files_info

        Raises:
            DiffFetchError: If fetching fails
        """
        logger.info(f"[{request_id}] Step 1/5: Fetching PR diff and file information")

        pr_data_complete = await diff_fetcher.fetch_pr_diff_and_files(
            pr_data.diff_url, pr_data.repository, pr_data.number
        )
        diff_content = pr_data_complete["diff_content"]
        files_info = pr_data_complete["files_info"]

        logger.info(
            f"[{request_id}] Step 1/5 completed: Fetched diff ({len(diff_content)} chars) and {files_info['total_files']} files"
        )

        return {"diff_content": diff_content, "files_info": files_info}

    async def _initialize_analyzers(
        self, request_id: str, analysis_engine, config
    ) -> List[str]:
        """
        Initialize and register security analyzers using injected services.

        Args:
            request_id: Request ID for logging
            analysis_engine: Injected analysis engine
            config: Service configuration

        Returns:
            List of registered analyzer names
        """
        logger.info(f"[{request_id}] Step 2/5: Initializing security analyzers")

        # Register analyzers based on configuration
        if config.enable_semgrep:
            semgrep_analyzer = SemgrepAnalyzer()
            analysis_engine.register_analyzer(semgrep_analyzer)

        if config.enable_ast_grep:
            ast_grep_analyzer = AstGrepAnalyzer()
            analysis_engine.register_analyzer(ast_grep_analyzer)

        if config.enable_linters:
            linter_analyzer = LinterAnalyzer()
            analysis_engine.register_analyzer(linter_analyzer)

        registered_analyzers = analysis_engine.get_registered_analyzers()
        logger.info(
            f"[{request_id}] Step 2/5 completed: Registered {len(registered_analyzers)} analyzers: {registered_analyzers}"
        )

        return registered_analyzers

    async def _run_security_analysis(
        self,
        pr_data: GitHubPRWebhookData,
        diff_content: str,
        files_info: Dict[str, Any],
        request_id: str,
        analysis_engine,
    ) -> Dict[str, Any]:
        """
        Run security analysis on PR data.

        Args:
            pr_data: GitHub PR webhook data
            diff_content: PR diff content
            files_info: File information
            request_id: Request ID for logging
            analysis_engine: Analysis engine

        Returns:
            Analysis report or error dict
        """
        logger.info(f"[{request_id}] Step 3/5: Running security analysis")

        try:
            analysis_report = await analysis_engine.analyze_pr(
                diff_content=diff_content,
                files_info=files_info,
                pr_id=pr_data.id,
                pr_number=pr_data.number,
                repository=pr_data.repository,
            )

            logger.info(
                f"[{request_id}] Step 3/5 completed: Analysis completed: {analysis_report.total_findings} findings, "
                f"{analysis_report.critical_findings} critical, "
                f"{analysis_report.high_findings} high severity"
            )

            return {"analysis_report": analysis_report}

        except AnalysisError as e:
            logger.error(f"[{request_id}] Analysis failed: {e}")
            return {
                "status": "error",
                "error": "Security analysis failed",
                "details": str(e),
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }
