"""PR validation service for determining if PRs should be analyzed."""

from typing import Any, Dict, Optional

from platyfend_ai.config.settings import settings
from platyfend_ai.models.github import GitHubPRWebhookData
from platyfend_ai.services.cache_service import analysis_cache
from platyfend_ai.utils.logging_config import get_logger

logger = get_logger(__name__)


class PRValidationService:
    """Service for validating if PRs should be analyzed."""

    async def validate_pr_for_analysis(
        self, pr_data: GitHubPRWebhookData, request_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Validate if PR should be analyzed based on various criteria.

        Args:
            pr_data: GitHub PR webhook data
            request_id: Request ID for logging

        Returns:
            None if PR should be analyzed, or dict with skip reason if it should be skipped
        """
        # Skip analysis if the webhook was triggered by the bot itself to prevent loops
        bot_usernames = ["platyfend-bot", "platyfend[bot]", "platyfend-test[bot]"]
        if pr_data.sender in bot_usernames:
            logger.info(
                f"[{request_id}] Skipping analysis - webhook triggered by bot user: {pr_data.sender}"
            )
            return {
                "status": "skipped",
                "reason": f"Bot-triggered event from {pr_data.sender}",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Only process specific PR actions that should trigger analysis
        allowed_actions = getattr(
            settings, "allowed_pr_actions", ["opened", "synchronize", "reopened"]
        )
        if pr_data.action not in allowed_actions:
            logger.info(
                f"[{request_id}] Skipping analysis - action '{pr_data.action}' not in allowed actions: {allowed_actions}"
            )
            return {
                "status": "skipped",
                "reason": f"Action '{pr_data.action}' not configured for analysis",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Skip analysis for draft PRs or closed PRs unless configured otherwise
        if pr_data.draft and not getattr(settings, "analyze_draft_prs", False):
            logger.info(f"[{request_id}] Skipping analysis for draft PR #{pr_data.number}")
            return {
                "status": "skipped",
                "reason": "Draft PR - analysis disabled",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        if pr_data.action == "closed":
            logger.info(f"[{request_id}] Skipping analysis for closed PR #{pr_data.number}")
            return {
                "status": "skipped",
                "reason": "PR closed",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        return None  # PR should be analyzed

    async def check_analysis_cache(
        self, pr_data: GitHubPRWebhookData, request_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Check if PR was recently analyzed to prevent duplicate processing.

        Args:
            pr_data: GitHub PR webhook data
            request_id: Request ID for logging

        Returns:
            None if analysis should proceed, or dict with skip reason if recently analyzed
        """
        logger.info(
            f"[{request_id}] Cache check for PR #{pr_data.number} in {pr_data.repository}"
        )

        # Check if recently analyzed using Redis cache
        cache_result = await analysis_cache.check_recent_analysis(
            pr_data.repository, pr_data.number
        )

        if cache_result:
            logger.info(f"[{request_id}] Skipping analysis - {cache_result['reason']}")
            return cache_result

        # Mark this PR as being analyzed
        success = await analysis_cache.mark_analysis_started(
            pr_data.repository, pr_data.number
        )

        if success:
            logger.info(f"[{request_id}] Marked PR #{pr_data.number} as being analyzed")
        else:
            logger.warning(f"[{request_id}] Failed to mark PR #{pr_data.number} in cache")

        return None  # Analysis should proceed
