import asyncio
from typing import Any, Dict, Optional, Union
from urllib.parse import urlparse

import httpx
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from platyfend_ai.config.settings import settings
from platyfend_ai.database import OrganizationService
from platyfend_ai.services.github_app_service import (
    GitHubAppTokenError,
    GitHubTokenService,
)
from platyfend_ai.services.http_client_service import get_github_client
from platyfend_ai.utils.error_handling import GitHubAPIError
from platyfend_ai.utils.error_handling import RateLimitError as PlatyfendRateLimitError
from platyfend_ai.utils.error_handling import handle_exceptions
from platyfend_ai.utils.logging_config import get_logger

logger = get_logger(__name__)


# Use PlatyfendError hierarchy instead of custom exceptions
# DiffFetchError -> GitHubAPIError
# RateLimitError -> PlatyfendRateLimitError


class DiffFetcher:
    """Service for fetching PR diffs and patches from GitHub"""

    def __init__(
        self, github_token: Optional[str] = None, use_dynamic_tokens: bool = True
    ):
        """
        Initialize the diff fetcher.

        Args:
            github_token: GitHub personal access token for authentication (fallback)
            use_dynamic_tokens: Whether to use dynamic token generation based on repository
        """
        self.github_token = github_token or settings.github_token
        self.use_dynamic_tokens = use_dynamic_tokens

        # Initialize services for dynamic token generation
        if self.use_dynamic_tokens:
            self.organization_service = OrganizationService()
            self.token_service = GitHubTokenService()

        self.base_headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": f"{settings.app_name}/{settings.version}",
        }

        # Only set static authorization if not using dynamic tokens
        if self.github_token and not self.use_dynamic_tokens:
            self.base_headers["Authorization"] = f"token {self.github_token}"

        logger.info(
            "DiffFetcher initialized",
            auth_mode="dynamic" if self.use_dynamic_tokens else "static",
            use_connection_pooling=True,
        )

    async def _get_token_for_repository(self, repository: str) -> Optional[str]:
        """
        Get the appropriate GitHub token for a repository.

        Args:
            repository: Repository name in format "owner/repo"

        Returns:
            GitHub token string or None if not available
        """
        if not self.use_dynamic_tokens:
            return self.github_token

        try:
            # Get installation ID for the repository
            installation_id = (
                await self.organization_service.get_installation_id_by_repo(repository)
            )

            if installation_id:
                # Get installation token from Next.js service
                token = await self.token_service.get_installation_token(installation_id)
                logger.info(
                    f"Retrieved dynamic token for repository {repository} (installation {installation_id})"
                )
                return token
            else:
                logger.warning(
                    f"No installation ID found for repository {repository}, falling back to static token"
                )
                return self.github_token

        except GitHubAppTokenError as e:
            logger.error(
                f"Failed to get dynamic token for repository {repository}: {e}"
            )
            logger.info("Falling back to static token")
            return self.github_token
        except Exception as e:
            logger.error(
                f"Unexpected error getting token for repository {repository}: {e}"
            )
            logger.info("Falling back to static token")
            return self.github_token

    def _extract_repository_from_url(self, url: str) -> Union[None, str]:
        """
        Extract repository name from GitHub URL.

        Args:
            url: GitHub URL

        Returns:
            Repository name in format "owner/repo" or None if not extractable
        """
        try:
            parsed_url = urlparse(url)
            if "github.com" not in parsed_url.netloc:
                return None

            # GitHub URLs typically have format: /owner/repo/...
            path_parts = parsed_url.path.strip("/").split("/")
            if len(path_parts) >= 2:
                owner = path_parts[0]
                repo = path_parts[1]
                # Validate owner and repo names
                if not owner or not repo or "/" in owner or "/" in repo:
                    logger.warning(
                        f"Invalid repository format extracted: {owner}/{repo}"
                    )
                    return None
                return f"{owner}/{repo}"
            return None
        except Exception as e:
            logger.error(f"Failed to extract repository from URL {url}: {e}")
            return None

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, PlatyfendRateLimitError)),
    )
    async def _make_request(
        self, url: str, headers: Optional[Dict[str, str]] = None
    ) -> httpx.Response:
        """
        Make an HTTP request with retry logic and rate limiting handling.

        Args:
            url: URL to fetch
            headers: Additional headers to include

        Returns:
            HTTP response

        Raises:
            DiffFetchError: If the request fails after retries
            RateLimitError: If rate limit is exceeded
        """
        request_headers = self.base_headers.copy()
        if headers:
            request_headers.update(headers)

        # Use HTTP client pool for better performance
        client = await get_github_client()

        try:
            logger.debug("Making GitHub API request", url=url)
            response = await client.get(url, headers=request_headers)

            # Handle rate limiting
            if response.status_code == 403 and "rate limit" in response.text.lower():
                reset_time = response.headers.get("X-RateLimit-Reset")
                logger.warning("Rate limit exceeded", reset_time=reset_time)
                raise PlatyfendRateLimitError(
                    f"GitHub API rate limit exceeded. Reset time: {reset_time}",
                    reset_time=reset_time,
                    component="diff_fetcher",
                    operation="fetch_diff",
                )

            # Handle other client errors
            if response.status_code == 401:
                raise GitHubAPIError(
                    "Authentication failed. Check GitHub token.",
                    status_code=401,
                    component="diff_fetcher",
                    operation="fetch_diff",
                )
            elif response.status_code == 404:
                raise GitHubAPIError(
                    "Resource not found. Check URL and permissions.",
                    status_code=404,
                    component="diff_fetcher",
                    operation="fetch_diff",
                )
            elif response.status_code >= 400:
                raise GitHubAPIError(
                    f"HTTP {response.status_code}: {response.text}",
                    status_code=response.status_code,
                    component="diff_fetcher",
                    operation="fetch_diff",
                )

            response.raise_for_status()
            return response

        except httpx.RequestError as e:
            logger.error("Request error", url=url, error=str(e))
            raise GitHubAPIError(
                f"Network error: {e}",
                component="diff_fetcher",
                operation="make_request",
            )

    async def fetch_diff(self, diff_url: str, repository: str, pr_number: int) -> str:
        """
        Fetch the diff content from a GitHub diff URL.

        Args:
            diff_url: URL to the diff (e.g., from PR webhook data)
            repository: Repository name in format "owner/repo" (optional, will be extracted from URL if not provided)

        Returns:
            Diff content as string

        Raises:
            DiffFetchError: If fetching fails
        """
        try:
            logger.info(f"Fetching diff from: {diff_url}")

            # Validate URL
            parsed_url = urlparse(diff_url)
            if not parsed_url.netloc or "github.com" not in parsed_url.netloc:
                raise GitHubAPIError(
                    f"Invalid GitHub URL: {diff_url}",
                    component="diff_fetcher",
                    operation="fetch_diff",
                )

            # Extract repository if not provided
            if not repository:
                extracted_repo = self._extract_repository_from_url(diff_url)
                if not extracted_repo:
                    raise GitHubAPIError(
                        f"Could not extract repository from URL: {diff_url}",
                        component="diff_fetcher",
                        operation="fetch_diff",
                    )
                repository = extracted_repo

            # Get appropriate token for the repository
            token = await self._get_token_for_repository(repository)
            # Set appropriate headers for diff format
            headers = {
                "Accept": "application/vnd.github.v3.diff",
            }

            if token:
                headers["Authorization"] = f"Bearer {token}"

            api_request = (
                f"https://api.github.com/repos/{repository}/pulls/{pr_number}.diff"
            )

            response = await self._make_request(api_request, headers)
            diff_content = response.text

            logger.info(f"Successfully fetched diff ({len(diff_content)} characters)")
            return diff_content

        except Exception as e:
            logger.error(f"Failed to fetch diff from {diff_url}: {e}")
            raise GitHubAPIError(
                f"Failed to fetch diff: {e}",
                component="diff_fetcher",
                operation="fetch_diff",
            )

    async def fetch_patch(self, patch_url: str) -> str:
        """
        Fetch the patch content from a GitHub patch URL.

        Args:
            patch_url: URL to the patch (e.g., from PR webhook data)

        Returns:
            Patch content as string

        Raises:
            DiffFetchError: If fetching fails
        """
        try:
            logger.info(f"Fetching patch from: {patch_url}")

            # Validate URL
            parsed_url = urlparse(patch_url)
            if not parsed_url.netloc or "github.com" not in parsed_url.netloc:
                raise GitHubAPIError(
                    f"Invalid GitHub URL: {patch_url}",
                    component="diff_fetcher",
                    operation="fetch_patch",
                )

            # Set appropriate headers for patch format
            headers = {"Accept": "application/vnd.github.v3.patch"}

            response = await self._make_request(patch_url, headers)
            patch_content = response.text

            logger.info(f"Successfully fetched patch ({len(patch_content)} characters)")
            return patch_content

        except Exception as e:
            logger.error(f"Failed to fetch patch from {patch_url}: {e}")
            raise GitHubAPIError(
                f"Failed to fetch patch: {e}",
                component="diff_fetcher",
                operation="fetch_patch",
            )

    async def fetch_pr_files(self, repository: str, pr_number: int) -> Dict[str, Any]:
        """
        Fetch the list of files changed in a PR using GitHub API.

        Args:
            repository: Repository name in format "owner/repo"
            pr_number: Pull request number

        Returns:
            Dictionary containing file information

        Raises:
            DiffFetchError: If fetching fails
        """
        try:
            url = f"https://api.github.com/repos/{repository}/pulls/{pr_number}/files"
            logger.info(f"Fetching PR files from: {url}")

            # Get appropriate token for the repository
            token = await self._get_token_for_repository(repository)

            # Set headers with dynamic token
            headers = {}
            if token:
                headers["Authorization"] = f"Bearer {token}"

            response = await self._make_request(url, headers)
            files_data = response.json()

            logger.info(
                f"Successfully fetched {len(files_data)} file(s) for PR #{pr_number}"
            )
            return {
                "files": files_data,
                "total_files": len(files_data),
                "additions": sum(file.get("additions", 0) for file in files_data),
                "deletions": sum(file.get("deletions", 0) for file in files_data),
                "changes": sum(file.get("changes", 0) for file in files_data),
            }

        except Exception as e:
            logger.error(f"Failed to fetch PR files for {repository}#{pr_number}: {e}")
            raise GitHubAPIError(
                f"Failed to fetch PR files: {e}",
                component="diff_fetcher",
                operation="fetch_pr_files",
            )

    async def fetch_pr_diff_and_files(
        self, diff_url: str, repository: str, pr_number: int
    ) -> Dict[str, Any]:
        """
        Fetch both diff content and file information for a PR.

        Args:
            diff_url: URL to the diff
            repository: Repository name in format "owner/repo"
            pr_number: Pull request number

        Returns:
            Dictionary containing both diff content and file information

        Raises:
            DiffFetchError: If fetching fails
        """
        try:
            logger.info(f"Fetching complete PR data for {repository}#{pr_number}")

            # Fetch both diff and files concurrently
            diff_task = self.fetch_diff(diff_url, repository, pr_number)
            files_task = self.fetch_pr_files(repository, pr_number)

            diff_content, files_info = await asyncio.gather(diff_task, files_task)

            result = {
                "diff_content": diff_content,
                "files_info": files_info,
                "repository": repository,
                "pr_number": pr_number,
            }

            logger.info(
                f"Successfully fetched complete PR data for {repository}#{pr_number}"
            )
            return result

        except Exception as e:
            logger.error(
                f"Failed to fetch complete PR data for {repository}#{pr_number}: {e}"
            )
            raise GitHubAPIError(
                f"Failed to fetch complete PR data: {e}",
                component="diff_fetcher",
                operation="fetch_pr_diff_and_files",
            )

    def __del__(self):
        """Cleanup when the object is destroyed"""
        logger.debug("DiffFetcher instance destroyed")
