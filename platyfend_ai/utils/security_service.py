import os
from typing import Optional

from fastapi import HTTPException, Security, status
from fastapi.security import APIKeyHeader

API_KEY: Optional[str] = os.getenv("API_KEY")


class SecurityService:
    """Service for handling API key authentication."""

    api_key_header: APIKeyHeader = APIKeyHeader(
        name="X-API-Key", auto_error=False, scheme_name="API Key"
    )

    @staticmethod
    async def get_api_key(api_key: Optional[str] = Security(api_key_header)) -> str:
        """
        Verify the general API key.

        Args:
            api_key: API key from request header

        Returns:
            Validated API key

        Raises:
            HTTPException: If API key is invalid or missing
        """

    @staticmethod
    async def get_api_key(api_key: Optional[str] = Security(api_key_header)) -> str:
        """
        Verify the general API key.

        Args:
            api_key: API key from request header

        Returns:
            Validated API key

        Raises:
            HTTPException: If API key is invalid or missing
        """

        import hmac

        if not api_key or not API_KEY or not hmac.compare_digest(api_key, API_KEY):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or missing API Key",
            )
        return api_key
