Metadata-Version: 2.4
Name: platyfend-ai
Version: 0.1.0
Summary: Next generation secure code review agent
Author-email: Platyfend Team <<EMAIL>>
Project-URL: Homepage, https://github.com/platyfend/platyfend-ai
Project-URL: Repository, https://github.com/platyfend/platyfend-ai
Project-URL: Issues, https://github.com/platyfend/platyfend-ai/issues
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: fastapi>=0.104.0
Requires-Dist: uvicorn[standard]>=0.24.0
Requires-Dist: gunicorn>=21.0.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: pydantic-settings>=2.0.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: aiohttp>=3.8.0
Requires-Dist: GitPython>=3.1.0
Requires-Dist: gidgethub>=5.0.0
Requires-Dist: semgrep>=1.20.0
Requires-Dist: ast-grep-py>=0.5.0
Requires-Dist: ruff>=0.0.270
Requires-Dist: openai>=1.0.0
Requires-Dist: motor>=3.3.0
Requires-Dist: pymongo>=4.5.0
Requires-Dist: redis>=5.0.0
Requires-Dist: PyJWT>=2.8.0
Requires-Dist: python-dotenv>=0.19.0
Requires-Dist: structlog>=23.0.0
Requires-Dist: tenacity>=8.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"
Requires-Dist: types-redis>=4.6.0; extra == "dev"

##BOT
TEST
