"""
FastAPI dependency injection providers for Platyfend AI services.

This module provides dependency injection for all major services to improve
testability, configuration management, and service lifecycle management.
"""

import logging
from typing import Optional

from fastapi import Depends

from platyfend_ai.config.settings import settings
from platyfend_ai.core.analysis_engine import AnalysisEngine, analysis_engine
from platyfend_ai.database.mongodb_service import MongoDBService, OrganizationService
from platyfend_ai.services import (
    <PERSON>ff<PERSON><PERSON><PERSON>,
    GitHubCommentService,
    GitHubTokenService,
    SecurityCommentGenerator,
)
from platyfend_ai.services.http_client_service import http_client_service
from platyfend_ai.utils.webhook_security import GitHubWebhookValidator

logger = logging.getLogger(__name__)


# Database Dependencies
def get_mongodb_service() -> MongoDBService:
    """
    Dependency provider for MongoDB service.

    Returns:
        MongoDBService instance configured with connection pooling
    """
    return MongoDBService()


def get_organization_service(
    mongodb_service: MongoDBService = Depends(get_mongodb_service),
) -> OrganizationService:
    """
    Dependency provider for Organization service.

    Args:
        mongodb_service: MongoDB service dependency

    Returns:
        OrganizationService instance
    """
    return OrganizationService(mongodb_service)


# Analysis Dependencies
def get_analysis_engine() -> AnalysisEngine:
    """
    Dependency provider for Analysis Engine.

    Returns:
        Global analysis engine instance
    """
    return analysis_engine


# GitHub Service Dependencies
def get_github_token_service() -> Optional[GitHubTokenService]:
    """
    Dependency provider for GitHub Token service.

    Returns:
        GitHubTokenService instance if configured, None otherwise
    """
    if not settings.github_app_id or not settings.github_private_key:
        return None

    # Note: Actual constructor parameters need to be checked
    # This is a placeholder implementation
    return GitHubTokenService()


def get_diff_fetcher() -> DiffFetcher:
    """
    Dependency provider for Diff Fetcher service.

    Returns:
        DiffFetcher instance configured with settings
    """
    if not settings.github_token:
        raise ValueError("GITHUB_TOKEN environment variable is required")

    return DiffFetcher(github_token=settings.github_token, use_dynamic_tokens=True)


def get_github_comment_service() -> GitHubCommentService:
    """
    Dependency provider for GitHub Comment service.

    Returns:
        GitHubCommentService instance
    """
    if not settings.github_token:
        raise ValueError("GITHUB_TOKEN environment variable is required")

    return GitHubCommentService(github_token=settings.github_token)


# AI Service Dependencies
def get_security_comment_generator() -> SecurityCommentGenerator:
    """
    Dependency provider for Security Comment Generator.

    Returns:
        SecurityCommentGenerator instance configured with settings
    """
    if not settings.openai_api_key:
        raise ValueError("OPENAI_API_KEY environment variable is required")

    return SecurityCommentGenerator(
        openai_api_key=settings.openai_api_key, model=settings.openai_model
    )


# New Service Dependencies
def get_idempotency_service():
    """
    Dependency provider for Idempotency service.

    Returns:
        IdempotencyService instance
    """
    from platyfend_ai.services.idempotency_service import IdempotencyService

    return IdempotencyService()


def get_pr_validation_service():
    """
    Dependency provider for PR Validation service.

    Returns:
        PRValidationService instance
    """
    from platyfend_ai.services.pr_validation_service import PRValidationService

    return PRValidationService()


def get_comment_processing_service(
    comment_generator: SecurityCommentGenerator = Depends(
        get_security_comment_generator
    ),
):
    """
    Dependency provider for Comment Processing service.

    Args:
        comment_generator: Security comment generator dependency

    Returns:
        CommentProcessingService instance
    """
    from platyfend_ai.services.comment_processing_service import (
        CommentProcessingService,
    )

    return CommentProcessingService(comment_generator)


def get_pr_analysis_service(
    validation_service=Depends(get_pr_validation_service),
    idempotency_service=Depends(get_idempotency_service),
    comment_processing_service=Depends(get_comment_processing_service),
):
    """
    Dependency provider for PR Analysis service.

    Args:
        validation_service: PR validation service dependency
        idempotency_service: Idempotency service dependency
        comment_processing_service: Comment processing service dependency

    Returns:
        PRAnalysisService instance
    """
    from platyfend_ai.services.pr_analysis_service import PRAnalysisService

    return PRAnalysisService(
        validation_service=validation_service,
        idempotency_service=idempotency_service,
        comment_processing_service=comment_processing_service,
    )


# Security Dependencies
def get_webhook_validator() -> GitHubWebhookValidator:
    """
    Dependency provider for Webhook Validator.

    Returns:
        GitHubWebhookValidator instance configured with settings
    """
    return GitHubWebhookValidator(settings.github_webhook_secret)


# Configuration Dependencies
class ServiceConfig:
    """Configuration container for service settings."""

    def __init__(self):
        self.github_token = settings.github_token
        self.openai_api_key = settings.openai_api_key
        self.mongodb_uri = settings.mongodb_uri
        self.mongodb_database = settings.mongodb_database
        self.github_app_id = settings.github_app_id
        self.github_private_key = settings.github_private_key
        self.github_webhook_secret = settings.github_webhook_secret
        self.openai_model = getattr(settings, "openai_model", "gpt-4o-mini")

        # Analysis settings
        self.enable_semgrep = getattr(settings, "enable_semgrep", True)
        self.enable_ast_grep = getattr(settings, "enable_ast_grep", True)
        self.enable_linters = getattr(settings, "enable_linters", True)

        # Feature flags
        self.enable_ai_comments = getattr(settings, "enable_ai_comments", True)
        self.analyze_draft_prs = getattr(settings, "analyze_draft_prs", False)

        # Allowed actions
        self.allowed_pr_actions = getattr(
            settings, "allowed_pr_actions", ["opened", "synchronize", "reopened"]
        )


def get_service_config() -> ServiceConfig:
    """
    Dependency provider for service configuration.

    Returns:
        ServiceConfig instance with current settings
    """
    return ServiceConfig()


# Composite Dependencies for Complex Operations
class AnalysisServices:
    """Container for all services needed for PR analysis."""

    def __init__(
        self,
        diff_fetcher: DiffFetcher,
        analysis_engine: AnalysisEngine,
        comment_generator: SecurityCommentGenerator,
        github_comment_service: GitHubCommentService,
        config: ServiceConfig,
    ):
        self.diff_fetcher = diff_fetcher
        self.analysis_engine = analysis_engine
        self.comment_generator = comment_generator
        self.github_comment_service = github_comment_service
        self.config = config


def get_analysis_services(
    diff_fetcher: DiffFetcher = Depends(get_diff_fetcher),
    analysis_engine: AnalysisEngine = Depends(get_analysis_engine),
    comment_generator: SecurityCommentGenerator = Depends(
        get_security_comment_generator
    ),
    github_comment_service: GitHubCommentService = Depends(get_github_comment_service),
    config: ServiceConfig = Depends(get_service_config),
) -> AnalysisServices:
    """
    Dependency provider for all analysis-related services.

    Args:
        diff_fetcher: Diff fetcher service
        analysis_engine: Analysis engine
        comment_generator: Comment generator service
        github_comment_service: GitHub comment service
        config: Service configuration

    Returns:
        AnalysisServices container with all dependencies
    """
    return AnalysisServices(
        diff_fetcher=diff_fetcher,
        analysis_engine=analysis_engine,
        comment_generator=comment_generator,
        github_comment_service=github_comment_service,
        config=config,
    )


# Health Check Dependencies
async def check_service_health() -> dict:
    """
    Check the health of all critical services.

    Returns:
        Dict with health status of each service
    """
    health_status = {
        "mongodb": "unknown",
        "github_token": "unknown",
        "openai": "unknown",
        "analysis_engine": "unknown",
    }

    try:
        # Check MongoDB
        mongodb_service = get_mongodb_service()
        is_healthy = await mongodb_service.health_check()
        health_status["mongodb"] = "healthy" if is_healthy else "unhealthy"
    except Exception as e:
        logger.error(f"MongoDB health check failed: {e}")
        health_status["mongodb"] = "unhealthy"

    try:
        # Check GitHub token
        if settings.github_token:
            health_status["github_token"] = "configured"
        else:
            health_status["github_token"] = "not_configured"
    except Exception as e:
        logger.error(f"GitHub token health check failed: {e}")
        health_status["github_token"] = "error"

    try:
        # Check OpenAI
        if settings.openai_api_key:
            health_status["openai"] = "configured"
        else:
            health_status["openai"] = "not_configured"
    except Exception as e:
        logger.error(f"OpenAI health check failed: {e}")
        health_status["openai"] = "error"

    try:
        # Check HTTP client pool
        client_health = await http_client_service.health_check()
        if client_health:
            healthy_count = sum(1 for status in client_health.values() if status)
            total_count = len(client_health)
            health_status["http_clients"] = (
                f"healthy ({healthy_count}/{total_count} clients)"
            )
        else:
            health_status["http_clients"] = "not_initialized"
    except Exception as e:
        logger.error(f"HTTP client health check failed: {e}")
        health_status["http_clients"] = "error"

    try:
        # Check Analysis Engine
        engine = get_analysis_engine()
        analyzers = engine.get_registered_analyzers()
        health_status["analysis_engine"] = f"healthy ({len(analyzers)} analyzers)"
    except Exception as e:
        logger.error(f"Analysis engine health check failed: {e}")
        health_status["analysis_engine"] = "unhealthy"

    return health_status
