"""
HTTP client service with connection pooling for efficient external API communication.

This module provides a centralized HTTP client pool manager that reuses connections
for better performance and resource utilization.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Any, Dict, Optional

import httpx

from platyfend_ai.config.settings import settings
from platyfend_ai.utils.logging_config import get_logger

logger = get_logger(__name__)


class HTTPClientPool:
    """HTTP client pool manager for efficient connection reuse."""

    _instance: Optional["HTTPClientPool"] = None
    _lock = asyncio.Lock()

    def __init__(self):
        """Initialize HTTP client pool."""
        self.clients: Dict[str, httpx.AsyncClient] = {}
        self._closed = False

    @classmethod
    async def get_instance(cls) -> "HTTPClientPool":
        """Get singleton instance of HTTP client pool."""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    async def get_client(self, client_type: str = "default") -> httpx.AsyncClient:
        """
        Get HTTP client from pool.

        Args:
            client_type: Type of client (default, github, openai, etc.)

        Returns:
            Configured httpx.AsyncClient instance
        """
        if self._closed:
            raise RuntimeError("HTTP client pool has been closed")

        if client_type not in self.clients:
            self.clients[client_type] = await self._create_client(client_type)

        return self.clients[client_type]

    async def _create_client(self, client_type: str) -> httpx.AsyncClient:
        """
        Create a new HTTP client with appropriate configuration.

        Args:
            client_type: Type of client to create

        Returns:
            Configured httpx.AsyncClient
        """
        # Base configuration for all clients
        base_config = {
            # Connection pool settings
            "limits": httpx.Limits(
                max_keepalive_connections=20,  # Keep up to 20 connections alive
                max_connections=100,  # Maximum total connections
                keepalive_expiry=30.0,  # Keep connections alive for 30 seconds
            ),
            # Timeout configuration
            "timeout": httpx.Timeout(
                connect=10.0,  # 10 seconds to establish connection
                read=30.0,  # 30 seconds to read response
                write=10.0,  # 10 seconds to send request
                pool=5.0,  # 5 seconds to get connection from pool
            ),
            # HTTP/2 support for better performance
            "http2": True,
            # Follow redirects
            "follow_redirects": True,
            # Retry configuration
            "transport": httpx.AsyncHTTPTransport(
                verify=True,
            ),
        }

        # Client-specific configurations
        if client_type == "github":
            base_config.update(
                {
                    "base_url": "https://api.github.com",
                    "headers": {
                        "Accept": "application/vnd.github.v3+json",
                        "User-Agent": f"{settings.app_name}/{settings.version}",
                    },
                    "timeout": httpx.Timeout(
                        connect=10.0,
                        read=settings.github_api_timeout,
                        write=10.0,
                        pool=5.0,
                    ),
                }
            )
        elif client_type == "openai":
            base_config.update(
                {
                    "base_url": "https://api.openai.com/v1",
                    "headers": {
                        "User-Agent": f"{settings.app_name}/{settings.version}",
                    },
                    "timeout": httpx.Timeout(
                        connect=10.0,
                        read=settings.comment_generation_timeout,
                        write=10.0,
                        pool=5.0,
                    ),
                }
            )
        elif client_type == "default":
            base_config.update(
                {
                    "headers": {
                        "User-Agent": f"{settings.app_name}/{settings.version}",
                    },
                }
            )

        client = httpx.AsyncClient(**base_config)

        logger.info(
            "HTTP client created",
            client_type=client_type,
            max_connections=100,
            max_keepalive=20,
            keepalive_expiry=30.0,
            http2_enabled=True,
        )

        return client

    async def close_client(self, client_type: str) -> None:
        """
        Close a specific client.

        Args:
            client_type: Type of client to close
        """
        if client_type in self.clients:
            await self.clients[client_type].aclose()
            del self.clients[client_type]
            logger.info("HTTP client closed", client_type=client_type)

    async def close_all(self) -> None:
        """Close all HTTP clients in the pool."""
        if self._closed:
            return

        for client_type, client in self.clients.items():
            try:
                await client.aclose()
                logger.info("HTTP client closed", client_type=client_type)
            except Exception as e:
                logger.warning(
                    "Error closing HTTP client", client_type=client_type, error=str(e)
                )

        self.clients.clear()
        self._closed = True
        logger.info("HTTP client pool closed")

    async def health_check(self) -> Dict[str, bool]:
        """
        Check health of all clients in the pool.

        Returns:
            Dictionary mapping client types to health status
        """
        health_status = {}

        for client_type, client in self.clients.items():
            try:
                # Simple health check - verify client is not closed
                if client.is_closed:
                    health_status[client_type] = False
                else:
                    health_status[client_type] = True
            except Exception as e:
                logger.warning(
                    "HTTP client health check failed",
                    client_type=client_type,
                    error=str(e),
                )
                health_status[client_type] = False

        return health_status


class HTTPClientService:
    """Service for managing HTTP clients with connection pooling."""

    def __init__(self):
        """Initialize HTTP client service."""
        self.pool: Optional[HTTPClientPool] = None

    async def get_client(self, client_type: str = "default") -> httpx.AsyncClient:
        """
        Get HTTP client from pool.

        Args:
            client_type: Type of client (default, github, openai, etc.)

        Returns:
            Configured httpx.AsyncClient instance
        """
        if self.pool is None:
            self.pool = await HTTPClientPool.get_instance()

        return await self.pool.get_client(client_type)

    @asynccontextmanager
    async def request_context(self, client_type: str = "default"):
        """
        Context manager for HTTP requests with automatic client management.

        Args:
            client_type: Type of client to use

        Yields:
            httpx.AsyncClient instance
        """
        client = await self.get_client(client_type)
        try:
            yield client
        except Exception:
            # Log error but don't close client - it's managed by the pool
            raise

    async def health_check(self) -> Dict[str, bool]:
        """
        Check health of HTTP client pool.

        Returns:
            Dictionary mapping client types to health status
        """
        if self.pool is None:
            return {}

        return await self.pool.health_check()


# Global HTTP client service instance
http_client_service = HTTPClientService()


# Convenience functions for common client types
async def get_github_client() -> httpx.AsyncClient:
    """Get GitHub API client."""
    return await http_client_service.get_client("github")


async def get_openai_client() -> httpx.AsyncClient:
    """Get OpenAI API client."""
    return await http_client_service.get_client("openai")


async def get_default_client() -> httpx.AsyncClient:
    """Get default HTTP client."""
    return await http_client_service.get_client("default")


# Export commonly used functions
__all__ = [
    "HTTPClientPool",
    "HTTPClientService",
    "http_client_service",
    "get_github_client",
    "get_openai_client",
    "get_default_client",
]
