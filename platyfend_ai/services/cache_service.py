"""
Redis-based cache service for Platyfend AI.

This service provides distributed caching capabilities with proper TTL,
cache invalidation, and distributed locking to replace the global in-memory cache.
"""

import asyncio
import json
import time
from typing import Any, Dict, Optional, Union

import redis.asyncio as redis
from redis.asyncio import Redis
from redis.exceptions import RedisError

from platyfend_ai.config.settings import settings
from platyfend_ai.utils.logging_config import get_logger

logger = get_logger(__name__)


class CacheService:
    """Redis-based cache service with distributed locking and TTL support."""

    def __init__(self, redis_url: Optional[str] = None):
        """
        Initialize the cache service.

        Args:
            redis_url: Redis connection URL (defaults to settings)
        """
        self.redis_url: str = redis_url or getattr(
            settings, "redis_url", "redis://localhost:6379"
        )
        self.redis_client: Optional[Redis] = None
        self.default_ttl: int = 300  # 5 minutes default TTL
        self.lock_timeout: int = 30  # 30 seconds lock timeout

    async def connect(self) -> None:
        """Connect to Redis."""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
            )

            # Test the connection
            if self.redis_client:
                await self.redis_client.ping()
                logger.info(
                    "Connected to Redis cache service", redis_url=self.redis_url
                )

        except RedisError as e:
            logger.error(
                "Failed to connect to Redis", error=str(e), redis_url=self.redis_url
            )
            # Fall back to in-memory cache if Redis is not available
            self.redis_client = None
            logger.warning("Falling back to in-memory cache")

    async def disconnect(self) -> None:
        """Disconnect from Redis."""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Disconnected from Redis")

    async def get(self, key: str) -> Optional[Any]:
        """
        Get a value from cache.

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found
        """
        if not self.redis_client:
            return None

        try:
            value = await self.redis_client.get(key)
            if value:
                return json.loads(value)
            return None

        except (RedisError, ValueError, TypeError) as e:
            logger.error(f"Failed to get cache key {key}: {e}")
            return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set a value in cache with TTL.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (defaults to default_ttl)

        Returns:
            True if successful, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            ttl = ttl or self.default_ttl
            serialized_value = json.dumps(value)

            result = await self.redis_client.setex(key, ttl, serialized_value)
            return bool(result)

        except (RedisError, ValueError, TypeError) as e:
            logger.error(f"Failed to set cache key {key}: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """
        Delete a key from cache.

        Args:
            key: Cache key to delete

        Returns:
            True if key was deleted, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.delete(key)
            return bool(result)

        except RedisError as e:
            logger.error(f"Failed to delete cache key {key}: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in cache.

        Args:
            key: Cache key to check

        Returns:
            True if key exists, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.exists(key)
            return bool(result)

        except RedisError as e:
            logger.error(f"Failed to check cache key {key}: {e}")
            return False

    async def get_ttl(self, key: str) -> Optional[int]:
        """
        Get the TTL of a key.

        Args:
            key: Cache key

        Returns:
            TTL in seconds, or None if key doesn't exist
        """
        if not self.redis_client:
            return None

        try:
            ttl = await self.redis_client.ttl(key)
            return ttl if ttl > 0 else None

        except RedisError as e:
            logger.error(f"Failed to get TTL for cache key {key}: {e}")
            return None

    async def acquire_lock(
        self, lock_key: str, timeout: Optional[int] = None
    ) -> Optional[str]:
        """
        Acquire a distributed lock.

        Args:
            lock_key: Lock identifier
            timeout: Lock timeout in seconds

        Returns:
            Lock token if acquired, None otherwise
        """
        if not self.redis_client:
            return None

        try:
            timeout = timeout or self.lock_timeout
            lock_token = f"lock_{int(time.time() * 1000)}"

            # Use SET with NX (only if not exists) and EX (expiration)
            result = await self.redis_client.set(
                f"lock:{lock_key}", lock_token, nx=True, ex=timeout
            )

            return lock_token if result else None

        except RedisError as e:
            logger.error(f"Failed to acquire lock {lock_key}: {e}")
            return None

    async def release_lock(self, lock_key: str, lock_token: str) -> bool:
        """
        Release a distributed lock.

        Args:
            lock_key: Lock identifier
            lock_token: Lock token from acquire_lock

        Returns:
            True if lock was released, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            # Lua script to atomically check and delete the lock
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """

            result = await self.redis_client.eval(
                lua_script, 1, f"lock:{lock_key}", lock_token
            )

            return bool(result)

        except RedisError as e:
            logger.error(f"Failed to release lock {lock_key}: {e}")
            return False

    async def clear_pattern(self, pattern: str) -> int:
        """
        Clear all keys matching a pattern.

        Args:
            pattern: Redis key pattern (e.g., "analysis:*")

        Returns:
            Number of keys deleted
        """
        if not self.redis_client:
            return 0

        try:
            keys = []
            async for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)

            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info(f"Cleared {deleted} cache keys matching pattern: {pattern}")
                return deleted

            return 0

        except RedisError as e:
            logger.error(f"Failed to clear cache pattern {pattern}: {e}")
            return 0

    async def health_check(self) -> bool:
        """
        Check if the cache service is healthy.

        Returns:
            True if healthy, False otherwise
        """
        if not self.redis_client:
            return False

        try:
            await self.redis_client.ping()
            return True

        except RedisError:
            return False


# Global cache service instance
cache_service = CacheService()


class AnalysisCache:
    """High-level cache interface for analysis operations."""

    def __init__(self, cache_service: CacheService) -> None:
        """
        Initialize analysis cache.

        Args:
            cache_service: Cache service instance
        """
        self.cache: CacheService = cache_service
        self.analysis_ttl: int = 300  # 5 minutes for analysis cache

    async def check_recent_analysis(
        self, repository: str, pr_number: int
    ) -> Optional[Dict[str, Any]]:
        """
        Check if PR was recently analyzed.

        Args:
            repository: Repository name
            pr_number: PR number

        Returns:
            Cache info if recently analyzed, None otherwise
        """
        cache_key = f"analysis:{repository}:{pr_number}"

        cached_data = await self.cache.get(cache_key)
        if cached_data:
            time_diff = time.time() - cached_data.get("timestamp", 0)
            return {
                "status": "skipped",
                "reason": f"Recently analyzed ({time_diff:.1f}s ago)",
                "pr_number": pr_number,
                "repository": repository,
                "cached_at": cached_data.get("timestamp"),
            }

        return None

    async def mark_analysis_started(self, repository: str, pr_number: int) -> bool:
        """
        Mark that analysis has started for a PR.

        Args:
            repository: Repository name
            pr_number: PR number

        Returns:
            True if marked successfully
        """
        cache_key = f"analysis:{repository}:{pr_number}"
        cache_data = {
            "timestamp": time.time(),
            "status": "in_progress",
            "repository": repository,
            "pr_number": pr_number,
        }

        return await self.cache.set(cache_key, cache_data, self.analysis_ttl)

    async def clear_analysis_cache(self, repository: Optional[str] = None) -> int:
        """
        Clear analysis cache entries.

        Args:
            repository: Clear only for specific repository (optional)

        Returns:
            Number of entries cleared
        """
        if repository:
            pattern = f"analysis:{repository}:*"
        else:
            pattern = "analysis:*"

        return await self.cache.clear_pattern(pattern)


# Global analysis cache instance
analysis_cache = AnalysisCache(cache_service)
