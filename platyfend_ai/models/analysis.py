from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class SeverityLevel(str, Enum):
    """Security finding severity levels"""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class AnalyzerType(str, Enum):
    """Types of analysis tools"""

    SEMGREP = "semgrep"
    AST_GREP = "ast_grep"
    LINTER = "linter"
    CUSTOM = "custom"


class FindingStatus(str, Enum):
    """Status of a security finding"""

    ACTIVE = "active"
    RESOLVED = "resolved"
    FALSE_POSITIVE = "false_positive"
    SUPPRESSED = "suppressed"


class CodeLocation(BaseModel):
    """Represents a location in code where a finding was detected"""

    file_path: str = Field(
        ..., description="Path to the file relative to repository root"
    )
    line_start: int = Field(..., description="Starting line number (1-based)")
    line_end: Optional[int] = Field(None, description="Ending line number (1-based)")
    column_start: Optional[int] = Field(
        None, description="Starting column number (1-based)"
    )
    column_end: Optional[int] = Field(
        None, description="Ending column number (1-based)"
    )

    def __str__(self) -> str:
        if self.line_end and self.line_end != self.line_start:
            return f"{self.file_path}:{self.line_start}-{self.line_end}"
        return f"{self.file_path}:{self.line_start}"


class SecurityFinding(BaseModel):
    """Represents a security finding from analysis tools"""

    id: UUID = Field(
        default_factory=uuid4, description="Unique identifier for the finding"
    )
    analyzer_type: AnalyzerType = Field(
        ..., description="Type of analyzer that generated this finding"
    )
    rule_id: str = Field(
        ..., description="Identifier of the rule that triggered this finding"
    )
    rule_name: str = Field(..., description="Human-readable name of the rule")
    severity: SeverityLevel = Field(..., description="Severity level of the finding")
    status: FindingStatus = Field(
        default=FindingStatus.ACTIVE, description="Current status of the finding"
    )

    # Location information
    location: CodeLocation = Field(
        ..., description="Location where the finding was detected"
    )

    # Finding details
    title: str = Field(..., description="Brief title describing the finding")
    description: str = Field(
        ..., description="Detailed description of the security issue"
    )
    message: str = Field(..., description="Message from the analyzer tool")

    # Code context
    code_snippet: Optional[str] = Field(
        None, description="Code snippet showing the problematic code"
    )

    # Remediation
    fix_suggestion: Optional[str] = Field(
        None, description="Suggested fix for the issue"
    )
    references: List[str] = Field(
        default_factory=list, description="External references (URLs, CVEs, etc.)"
    )

    # Metadata
    confidence: Optional[float] = Field(
        None, ge=0.0, le=1.0, description="Confidence score (0.0-1.0)"
    )
    tags: List[str] = Field(
        default_factory=list, description="Tags associated with the finding"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional analyzer-specific metadata"
    )

    # Timestamps
    detected_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When the finding was detected",
    )

    def __str__(self) -> str:
        return f"{self.severity.value.upper()}: {self.title} at {self.location}"


class AnalysisResult(BaseModel):
    """Results from running an analysis tool"""

    id: UUID = Field(
        default_factory=uuid4, description="Unique identifier for the analysis result"
    )
    analyzer_type: AnalyzerType = Field(
        ..., description="Type of analyzer that produced this result"
    )
    analyzer_version: Optional[str] = Field(
        None, description="Version of the analyzer tool"
    )

    # Analysis metadata
    started_at: datetime = Field(..., description="When the analysis started")
    completed_at: datetime = Field(..., description="When the analysis completed")
    duration_seconds: float = Field(
        ..., description="Duration of the analysis in seconds"
    )

    # Results
    findings: List[SecurityFinding] = Field(
        default_factory=list, description="Security findings discovered"
    )
    files_analyzed: List[str] = Field(
        default_factory=list, description="List of files that were analyzed"
    )

    # Status and errors
    success: bool = Field(
        ..., description="Whether the analysis completed successfully"
    )
    error_message: Optional[str] = Field(
        None, description="Error message if analysis failed"
    )
    warnings: List[str] = Field(
        default_factory=list, description="Non-fatal warnings during analysis"
    )

    # Statistics
    total_findings: int = Field(default=0, description="Total number of findings")
    findings_by_severity: Dict[SeverityLevel, int] = Field(
        default_factory=dict, description="Count of findings by severity"
    )

    # Raw output (for debugging)
    raw_output: Optional[str] = Field(
        None, description="Raw output from the analyzer tool"
    )

    def model_post_init__(self, __context):
        """Calculate derived fields after initialization"""
        self.total_findings = len(self.findings)
        self.findings_by_severity = {}
        for severity in SeverityLevel:
            self.findings_by_severity[severity] = len(
                [f for f in self.findings if f.severity == severity]
            )


class ReviewComment(BaseModel):
    """Represents a review comment to be posted to a PR"""

    id: UUID = Field(
        default_factory=uuid4, description="Unique identifier for the comment"
    )

    # Comment content
    body: str = Field(..., description="The comment body text (Markdown supported)")

    # Location (for inline comments)
    file_path: Optional[str] = Field(None, description="File path for inline comments")
    line: Optional[int] = Field(None, description="Line number for inline comments")

    # Associated findings
    finding_ids: List[UUID] = Field(
        default_factory=list, description="IDs of findings this comment addresses"
    )

    # Comment metadata
    comment_type: str = Field(
        default="review", description="Type of comment (review, suggestion, etc.)"
    )
    severity: Optional[SeverityLevel] = Field(
        None, description="Severity if comment is about a security issue"
    )

    # Timestamps
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When the comment was created",
    )

    def __str__(self) -> str:
        location = (
            f" at {self.file_path}:{self.line}" if self.file_path and self.line else ""
        )
        return f"Comment{location}: {self.body[:50]}..."


class PRAnalysisReport(BaseModel):
    """Complete analysis report for a Pull Request"""

    id: UUID = Field(
        default_factory=uuid4, description="Unique identifier for the analysis report"
    )

    # PR information
    pr_id: int = Field(..., description="Pull request ID")
    pr_number: int = Field(..., description="Pull request number")
    repository: str = Field(..., description="Repository name (owner/repo)")

    # Analysis metadata
    started_at: datetime = Field(..., description="When the analysis started")
    completed_at: Optional[datetime] = Field(
        None, description="When the analysis completed"
    )

    # Analysis results
    analysis_results: List[AnalysisResult] = Field(
        default_factory=list, description="Results from each analyzer"
    )

    # Generated comments
    review_comments: List[ReviewComment] = Field(
        default_factory=list, description="Generated review comments"
    )

    # Summary statistics
    total_findings: int = Field(
        default=0, description="Total findings across all analyzers"
    )
    critical_findings: int = Field(default=0, description="Number of critical findings")
    high_findings: int = Field(
        default=0, description="Number of high severity findings"
    )

    # Status
    success: bool = Field(
        default=True, description="Whether the overall analysis was successful"
    )
    errors: List[str] = Field(
        default_factory=list, description="Any errors that occurred during analysis"
    )

    def calculate_summary(self):
        """Calculate summary statistics from analysis results"""
        all_findings = []
        for result in self.analysis_results:
            all_findings.extend(result.findings)

        self.total_findings = len(all_findings)
        self.critical_findings = len(
            [f for f in all_findings if f.severity == SeverityLevel.CRITICAL]
        )
        self.high_findings = len(
            [f for f in all_findings if f.severity == SeverityLevel.HIGH]
        )
        return self

    def __str__(self) -> str:
        return f"Analysis Report for PR #{self.pr_number} ({self.repository}): {self.total_findings} findings"
