"""GitHub App token service that uses Next.js token endpoint."""

import logging
import os
from typing import Optional

import httpx
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)


class GitHubAppTokenError(Exception):
    """Exception raised when GitHub App token operations fail."""

    pass


class GitHubTokenService:
    """Service for fetching GitHub App installation tokens from Next.js endpoint."""

    def __init__(self, token_service_url: Optional[str] = None):
        """
        Initialize GitHub token service.

        Args:
            token_service_url: Base URL for the Next.js token service
        """
        self.token_service_url = token_service_url or os.getenv(
            "GITHUB_TOKEN_SERVICE_URL", "http://localhost:3001"
        )

        # HTTP client configuration
        self.timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=5.0)

        logger.info(
            f"GitHub token service initialized with URL: {self.token_service_url}"
        )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        reraise=True,
    )
    async def get_installation_token(self, installation_id: int) -> str:
        """
        Get GitHub App installation token from Next.js service.

        Args:
            installation_id: GitHub App installation ID

        Returns:
            GitHub access token string

        Raises:
            GitHubAppTokenError: If token retrieval fails
        """
        try:
            logger.info(
                f"Fetching installation token for installation ID: {installation_id}"
            )

            # Make request to Next.js token endpoint
            url = f"{self.token_service_url}/api/github/token/{installation_id}"

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url)

                # Handle various error cases
                if response.status_code == 404:
                    raise GitHubAppTokenError(
                        f"Installation ID {installation_id} not found"
                    )
                elif response.status_code == 401:
                    raise GitHubAppTokenError("Authentication failed with GitHub")
                elif response.status_code == 403:
                    raise GitHubAppTokenError(
                        "Access forbidden - check GitHub App permissions"
                    )
                elif response.status_code >= 500:
                    raise GitHubAppTokenError(
                        f"Token service error: {response.status_code}"
                    )
                elif response.status_code >= 400:
                    error_text = response.text
                    raise GitHubAppTokenError(
                        f"Token request failed ({response.status_code}): {error_text}"
                    )

                response.raise_for_status()
                token_data = response.json()

                # Extract access token from response
                access_token = token_data.get("access_token") or token_data.get("token")

                if not access_token:
                    logger.error(f"No access token in response: {token_data}")
                    raise GitHubAppTokenError(
                        "No access token in response from token service"
                    )

                logger.info(
                    f"Successfully retrieved installation token for installation ID: {installation_id}"
                )
                return access_token

        except httpx.RequestError as e:
            logger.error(
                f"Network error fetching token for installation {installation_id}: {e}"
            )
            raise GitHubAppTokenError(f"Network error: {e}")
        except GitHubAppTokenError:
            raise
        except Exception as e:
            logger.error(
                f"Unexpected error fetching token for installation {installation_id}: {e}"
            )
            raise GitHubAppTokenError(f"Token retrieval failed: {e}")


# Convenience function for quick token retrieval
async def get_github_installation_token(installation_id: int) -> str:
    """
    Convenience function to get a GitHub App installation token.

    Args:
        installation_id: GitHub App installation ID

    Returns:
        GitHub access token string
    """
    service = GitHubTokenService()
    return await service.get_installation_token(installation_id)
