"""Idempotency service for handling webhook delivery tracking."""

import time
from typing import Optional

from platyfend_ai.database.mongodb_service import MongoDBService
from platyfend_ai.models.github import GitHubPRWebhookData
from platyfend_ai.utils.logging_config import get_logger

logger = get_logger(__name__)


class IdempotencyService:
    """Service for handling webhook idempotency using X-GitHub-Delivery header."""

    def __init__(self):
        self.mongodb_service = MongoDBService()
        self.collection_name = "webhook_deliveries"
        self.ttl_seconds = 86400  # 24 hours TTL for delivery records

    async def is_delivery_processed(self, delivery_id: str) -> bool:
        """
        Check if a webhook delivery has already been processed.

        Args:
            delivery_id: GitHub delivery ID from X-GitHub-Delivery header

        Returns:
            True if already processed, False otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            # Check if delivery ID exists
            existing = await collection.find_one({"delivery_id": delivery_id})

            if existing:
                logger.info(
                    f"Webhook delivery {delivery_id} already processed at {existing.get('processed_at')}"
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking delivery idempotency: {e}")
            # On error, allow processing to continue (fail open)
            return False

    async def mark_delivery_processed(
        self, delivery_id: str, pr_data: GitHubPRWebhookData, status: str = "success"
    ) -> bool:
        """
        Mark a webhook delivery as processed.

        Args:
            delivery_id: GitHub delivery ID from X-GitHub-Delivery header
            pr_data: PR webhook data for context
            status: Processing status (success, error, skipped)

        Returns:
            True if successfully marked, False otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            # Create TTL index if it doesn't exist
            await collection.create_index(
                "processed_at", expireAfterSeconds=self.ttl_seconds
            )

            delivery_record = {
                "delivery_id": delivery_id,
                "repository": pr_data.repository,
                "pr_number": pr_data.number,
                "pr_id": pr_data.id,
                "action": pr_data.action,
                "status": status,
                "processed_at": time.time(),
            }

            # Insert the delivery record
            await collection.insert_one(delivery_record)

            logger.info(
                f"Marked webhook delivery {delivery_id} as processed with status: {status}"
            )
            return True

        except Exception as e:
            logger.error(f"Error marking delivery as processed: {e}")
            return False


async def mark_delivery_if_present(
    idempotency_service: IdempotencyService,
    delivery_id: Optional[str], 
    pr_data: GitHubPRWebhookData, 
    status: str
) -> None:
    """Helper function to mark delivery as processed if delivery_id is present."""
    if delivery_id:
        try:
            await idempotency_service.mark_delivery_processed(
                delivery_id, pr_data, status
            )
        except Exception as mark_error:
            logger.error(
                f"Failed to mark delivery {delivery_id} as {status}: {mark_error}"
            )
