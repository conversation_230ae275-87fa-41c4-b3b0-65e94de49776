import time
from contextlib import asynccontextmanager
from typing import Union

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from platyfend_ai.api.routes.endpoints import api_router
from platyfend_ai.config.settings import settings
from platyfend_ai.database.mongodb_service import MongoDBConnectionPool
from platyfend_ai.services.cache_service import cache_service
from platyfend_ai.services.http_client_service import HTTPClientPool
from platyfend_ai.utils.logging_config import (
    RequestContextMiddleware,
    configure_structlog,
    get_logger,
)

# Configure structured logging
configure_structlog()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    logger.info(f"Starting up in {settings.environment} mode...")

    # Initialize cache service
    try:
        await cache_service.connect()
        logger.info("Cache service initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize cache service: {e}")
        logger.info("Application will continue with fallback caching")

    # Initialize MongoDB connection pool
    if settings.mongodb_uri:
        try:
            mongodb_pool = await MongoDBConnectionPool.get_instance()
            logger.info("MongoDB connection pool initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize MongoDB connection pool: {e}")
    else:
        logger.info("MongoDB URI not configured - skipping MongoDB initialization")

    # Initialize HTTP client pool
    try:
        http_pool = await HTTPClientPool.get_instance()
        logger.info("HTTP client pool initialized")
    except Exception as e:
        logger.warning(f"Failed to initialize HTTP client pool: {e}")

    yield

    # Cleanup cache service
    try:
        await cache_service.disconnect()
        logger.info("Cache service disconnected")
    except Exception as e:
        logger.error(f"Error disconnecting cache service: {e}")

    # Cleanup MongoDB connection pool
    if settings.mongodb_uri:
        try:
            mongodb_pool = await MongoDBConnectionPool.get_instance()
            await mongodb_pool.disconnect()
            logger.info("MongoDB connection pool closed")
        except Exception as e:
            logger.warning(f"Error closing MongoDB connection pool: {e}")

    # Cleanup HTTP client pool
    try:
        http_pool = await HTTPClientPool.get_instance()
        await http_pool.close_all()
        logger.info("HTTP client pool closed")
    except Exception as e:
        logger.warning(f"Error closing HTTP client pool: {e}")

    logger.info("Shutting down...")


# Create FastAPI app with environment-specific settings
app = FastAPI(
    title=settings.app_name,
    description=settings.description,
    version=settings.version,
    docs_url=settings.docs_url,
    redoc_url=settings.redoc_url,
    lifespan=lifespan,
)

# Environment-specific CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)

# Add request context middleware for structured logging
app.add_middleware(RequestContextMiddleware)


# Add request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Include API router
app.include_router(api_router, prefix="/api/v1")


# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    return {
        "message": "Welcome to PLATYFEND-AI API",
        "docs": "/docs",
        "version": settings.version,
    }


# Health check endpoints
@app.get("/health", tags=["Health"])
async def health_check():
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.version,
        "environment": settings.environment,
    }


@app.get("/ready", tags=["Health"])
async def readiness_check():
    # Add checks for dependencies (database, external APIs, etc.)
    return {"status": "ready"}


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(_request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred. Please try again later."},
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "platyfend_ai.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["platyfend_ai"],
    )
