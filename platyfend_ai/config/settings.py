from enum import Enum
from typing import List, Optional
from urllib.parse import urlparse

from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings


class Environment(str, Enum):
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"


class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class PlatyfendSettings(BaseSettings):
    """Platyfend AI configuration with proper Pydantic validation."""

    # App metadata
    app_name: str = Field(default="PLATYFEND-AI", description="Application name")
    version: str = Field(default="0.1.0", description="Application version")
    description: str = Field(
        default="Next generation secure code review agent",
        description="Application description",
    )

    # Environment detection
    environment: Environment = Field(
        default=Environment.DEVELOPMENT, description="Application environment"
    )

    # Server settings
    host: str = Field(default="0.0.0.0", description="Server host address")
    port: int = Field(default=8000, ge=1, le=65535, description="Server port number")
    debug: bool = Field(default=True, description="Enable debug mode")

    # Security - Required in production, defaults in development
    secret_key: str = Field(
        default="dev-secret-key-change-in-production",
        min_length=1,
        description="Secret key for cryptographic operations",
    )
    api_key: str = Field(
        default="dev-api-key", min_length=1, description="API key for authentication"
    )

    # External APIs - Load from environment
    github_token: Optional[str] = Field(
        default=None, description="GitHub personal access token"
    )
    openai_api_key: Optional[str] = Field(
        default=None, description="OpenAI API key for AI-powered comments"
    )
    openai_model: str = Field(
        default="gpt-4o-mini", description="OpenAI model to use for comment generation"
    )

    # GitHub App Configuration - Load from environment
    github_app_id: Optional[str] = Field(
        default=None, description="GitHub App ID for GitHub App authentication"
    )
    github_private_key: Optional[str] = Field(
        default=None, description="GitHub App private key (PEM format)"
    )
    github_webhook_secret: Optional[str] = Field(
        default=None, description="GitHub webhook secret for signature validation"
    )

    # Database Configuration - Load from environment
    mongodb_uri: Optional[str] = Field(
        default=None, description="MongoDB connection URI"
    )
    mongodb_database: str = Field(
        default="platyfend_dev", description="MongoDB database name"
    )
    database_url: str = Field(
        default="sqlite:///./dev.db", description="Database URL for SQLAlchemy"
    )

    # Cache Configuration - Load from environment
    redis_url: str = Field(
        default="redis://localhost:6379", description="Redis connection URL"
    )

    # GitLab Configuration (Optional)
    gitlab_client_id: Optional[str] = Field(
        default=None, description="GitLab OAuth client ID"
    )
    gitlab_client_secret: Optional[str] = Field(
        default=None, description="GitLab OAuth client secret"
    )
    gitlab_redirect_uri: Optional[str] = Field(
        default=None, description="GitLab OAuth redirect URI"
    )
    gitlab_secret: Optional[str] = Field(
        default=None, description="GitLab webhook secret"
    )

    # Analysis tools configuration
    semgrep_timeout: int = Field(
        default=300,
        ge=10,
        le=3600,
        description="Timeout for Semgrep analysis in seconds",
    )
    ast_grep_timeout: int = Field(
        default=60, ge=5, le=600, description="Timeout for ast-grep analysis in seconds"
    )
    linter_timeout: int = Field(
        default=120, ge=10, le=600, description="Timeout for linter analysis in seconds"
    )

    # Analysis engine settings
    max_concurrent_analyzers: int = Field(
        default=3, ge=1, le=10, description="Maximum number of concurrent analyzers"
    )
    analysis_timeout: int = Field(
        default=600, ge=60, le=3600, description="Overall analysis timeout in seconds"
    )

    # Comment generation settings
    comment_generation_timeout: int = Field(
        default=30,
        ge=5,
        le=300,
        description="Timeout for comment generation in seconds",
    )
    max_comment_length: int = Field(
        default=2000,
        ge=100,
        le=10000,
        description="Maximum length of generated comments",
    )

    # GitHub API settings
    github_api_timeout: int = Field(
        default=30, ge=5, le=300, description="GitHub API request timeout in seconds"
    )
    github_max_retries: int = Field(
        default=3, ge=0, le=10, description="Maximum number of GitHub API retries"
    )
    github_retry_delay: int = Field(
        default=5,
        ge=1,
        le=60,
        description="Delay between GitHub API retries in seconds",
    )

    # MongoDB settings
    mongodb_timeout: int = Field(
        default=10, ge=1, le=60, description="MongoDB connection timeout in seconds"
    )

    # Rate limiting
    github_rate_limit_buffer: int = Field(
        default=100, ge=10, le=1000, description="GitHub API rate limit buffer"
    )

    # Security settings
    max_diff_size_mb: int = Field(
        default=10, ge=1, le=100, description="Maximum diff size in megabytes"
    )
    max_files_per_pr: int = Field(
        default=100, ge=1, le=1000, description="Maximum number of files per PR"
    )

    # Logging configuration
    log_level: LogLevel = Field(default=LogLevel.INFO, description="Logging level")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string",
    )
    log_file: Optional[str] = Field(
        default=None, description="Log file path (optional)"
    )

    # Feature flags
    enable_ai_comments: bool = Field(
        default=True, description="Enable AI-powered comment generation"
    )
    enable_semgrep: bool = Field(default=True, description="Enable Semgrep analysis")
    enable_ast_grep: bool = Field(default=True, description="Enable ast-grep analysis")
    enable_linters: bool = Field(default=True, description="Enable linter analysis")

    # CORS settings
    allowed_origins: List[str] = Field(
        default=["*"], description="Allowed CORS origins"
    )
    allowed_methods: List[str] = Field(
        default=["*"], description="Allowed CORS methods"
    )
    allowed_headers: List[str] = Field(
        default=["*"], description="Allowed CORS headers"
    )

    # FastAPI docs
    docs_url: Optional[str] = Field(default="/docs", description="FastAPI docs URL")
    redoc_url: Optional[str] = Field(default="/redoc", description="ReDoc URL")

    # Timeouts
    request_timeout: int = Field(
        default=30, ge=1, le=300, description="General request timeout in seconds"
    )

    # Pydantic validators
    @field_validator("secret_key")
    @classmethod
    def validate_secret_key(cls, v: str, info) -> str:
        """Validate secret key based on environment."""
        if info.data.get("environment") == Environment.PRODUCTION:
            if v == "dev-secret-key-change-in-production":
                raise ValueError(
                    "Must set SECRET_KEY environment variable in production"
                )
            if len(v) < 32:
                raise ValueError(
                    "SECRET_KEY must be at least 32 characters long in production"
                )
        return v

    @field_validator("api_key")
    @classmethod
    def validate_api_key(cls, v: str, info) -> str:
        """Validate API key based on environment."""
        if info.data.get("environment") == Environment.PRODUCTION:
            if v == "dev-api-key":
                raise ValueError("Must set API_KEY environment variable in production")
            if len(v) < 16:
                raise ValueError(
                    "API_KEY must be at least 16 characters long in production"
                )
        return v

    @field_validator("redis_url")
    @classmethod
    def validate_redis_url(cls, v: str) -> str:
        """Validate Redis URL format."""
        parsed = urlparse(v)
        if not parsed.scheme or parsed.scheme not in ["redis", "rediss"]:
            raise ValueError("Redis URL must start with redis:// or rediss://")
        return v

    @field_validator("mongodb_uri")
    @classmethod
    def validate_mongodb_uri(cls, v: Optional[str]) -> Optional[str]:
        """Validate MongoDB URI format."""
        if v is not None:
            parsed = urlparse(v)
            if not parsed.scheme or parsed.scheme not in ["mongodb", "mongodb+srv"]:
                raise ValueError(
                    "MongoDB URI must start with mongodb:// or mongodb+srv://"
                )
        return v

    @field_validator("github_private_key")
    @classmethod
    def validate_github_private_key(cls, v: Optional[str]) -> Optional[str]:
        """Validate GitHub private key format."""
        if v is not None:
            if not v.startswith("-----BEGIN") or not v.endswith("-----"):
                raise ValueError("GitHub private key must be in PEM format")
        return v

    @field_validator("openai_api_key")
    @classmethod
    def validate_openai_api_key(cls, v: Optional[str]) -> Optional[str]:
        """Validate OpenAI API key format."""
        if v is not None:
            if not v.startswith("sk-"):
                raise ValueError("OpenAI API key must start with 'sk-'")
            if len(v) < 20:
                raise ValueError("OpenAI API key appears to be too short")
        return v

    @model_validator(mode="after")
    def validate_production_requirements(self) -> "PlatyfendSettings":
        """Validate production-specific requirements."""
        if self.environment == Environment.PRODUCTION:
            required_fields = {
                "github_token": "GitHub token",
                "openai_api_key": "OpenAI API key",
                "github_webhook_secret": "GitHub webhook secret",
                "mongodb_uri": "MongoDB URI",
            }

            for field, description in required_fields.items():
                if getattr(self, field) is None:
                    raise ValueError(
                        f"{description} is required in production environment"
                    )

        return self

    class Config:
        """Pydantic configuration."""

        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        validate_assignment = True
        extra = "forbid"  # Forbid extra fields


# Global settings instance - loads from environment variables automatically
settings = PlatyfendSettings()
