[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "platyfend-ai"
version = "0.1.0"
description = "Next generation secure code review agent"
authors = [
    {name = "Platyfend Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "gunicorn>=21.0.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "httpx>=0.25.0",
    "aiohttp>=3.8.0",
    "GitPython>=3.1.0",
    "gidgethub>=5.0.0",
    "openai>=1.0.0",
    "motor>=3.3.0",
    "pymongo>=4.5.0",
    "redis>=5.0.0",
    "PyJWT>=2.8.0",
    "python-dotenv>=0.19.0",
    "structlog>=23.0.0",
    "tenacity>=8.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "types-redis>=4.6.0",
    "semgrep>=1.20.0",
    "ast-grep-py>=0.5.0",
    "ruff>=0.0.270",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "types-redis>=4.6.0",
]

[project.urls]
Homepage = "https://github.com/platyfend/platyfend-ai"
Repository = "https://github.com/platyfend/platyfend-ai"
Issues = "https://github.com/platyfend/platyfend-ai/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["platyfend_ai*"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
