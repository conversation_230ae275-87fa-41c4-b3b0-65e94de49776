"""
Structured logging configuration for Platyfend AI.

This module configures structlog for consistent, structured log output with
correlation IDs, request tracing, and proper log levels throughout the application.
"""

import logging
import sys
import uuid
from contextvars import ContextVar
from typing import Any, Dict, Optional

import structlog
from structlog.types import EventDict, Processor

from platyfend_ai.config.settings import settings

# Context variables for request tracing
request_id_context: ContextVar[Optional[str]] = ContextVar("request_id", default=None)
user_id_context: ContextVar[Optional[str]] = ContextVar("user_id", default=None)
operation_context: ContextVar[Optional[str]] = ContextVar("operation", default=None)


def add_request_context(
    logger: Any, method_name: str, event_dict: EventDict
) -> EventDict:
    """
    Add request context to log entries.

    Args:
        logger: The logger instance
        method_name: The method name being called
        event_dict: The event dictionary

    Returns:
        Updated event dictionary with request context
    """
    # Add request ID if available
    request_id = request_id_context.get()
    if request_id:
        event_dict["request_id"] = request_id

    # Add user ID if available
    user_id = user_id_context.get()
    if user_id:
        event_dict["user_id"] = user_id

    # Add operation context if available
    operation = operation_context.get()
    if operation:
        event_dict["operation"] = operation

    return event_dict


def add_severity_level(
    logger: Any, method_name: str, event_dict: EventDict
) -> EventDict:
    """
    Add severity level mapping for better log analysis.

    Args:
        logger: The logger instance
        method_name: The method name being called
        event_dict: The event dictionary

    Returns:
        Updated event dictionary with severity mapping
    """
    level = event_dict.get("level", "").upper()

    # Map log levels to severity
    severity_map = {
        "DEBUG": "debug",
        "INFO": "info",
        "WARNING": "warning",
        "ERROR": "error",
        "CRITICAL": "critical",
    }

    event_dict["severity"] = severity_map.get(level, "unknown")
    return event_dict


def add_component_info(
    logger: Any, method_name: str, event_dict: EventDict
) -> EventDict:
    """
    Add component information based on logger name.

    Args:
        logger: The logger instance
        method_name: The method name being called
        event_dict: The event dictionary

    Returns:
        Updated event dictionary with component info
    """
    logger_name = getattr(logger, "name", "unknown")

    # Extract component from logger name
    if "platyfend_ai" in logger_name:
        parts = logger_name.split(".")
        if len(parts) >= 2:
            event_dict["component"] = parts[1]  # e.g., "api", "services", "analyzers"
        if len(parts) >= 3:
            event_dict["module"] = parts[2]  # e.g., "github", "diff_fetcher"

    return event_dict


def filter_sensitive_data(
    logger: Any, method_name: str, event_dict: EventDict
) -> EventDict:
    """
    Filter out sensitive data from log entries.

    Args:
        logger: The logger instance
        method_name: The method name being called
        event_dict: The event dictionary

    Returns:
        Updated event dictionary with sensitive data filtered
    """
    sensitive_keys = {
        "password",
        "token",
        "key",
        "secret",
        "credential",
        "authorization",
        "auth",
        "api_key",
        "private_key",
    }

    def filter_dict(data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively filter sensitive data from dictionaries."""
        filtered = {}
        for key, value in data.items():
            key_lower = key.lower()

            # Check if key contains sensitive information
            if any(sensitive in key_lower for sensitive in sensitive_keys):
                filtered[key] = "[REDACTED]"
            elif isinstance(value, dict):
                filtered[key] = filter_dict(value)
            elif isinstance(value, str) and any(
                sensitive in key_lower for sensitive in sensitive_keys
            ):
                # Only redact if the key explicitly indicates sensitive data
                filtered[key] = "[REDACTED]"
            else:
                filtered[key] = value

        return filtered

    # Filter the event dict itself
    for key, value in list(event_dict.items()):
        if isinstance(value, dict):
            event_dict[key] = filter_dict(value)
        elif isinstance(value, str) and key.lower() in sensitive_keys:
            event_dict[key] = "[REDACTED]"

    return event_dict


def configure_structlog() -> None:
    """Configure structlog for the application."""

    # Determine if we're in development mode
    is_development = settings.environment.value == "development"

    # Configure processors
    processors: list[Processor] = [
        # Add standard library log level
        structlog.stdlib.add_log_level,
        # Add timestamp
        structlog.processors.TimeStamper(fmt="ISO"),
        # Add request context
        add_request_context,
        # Add severity level
        add_severity_level,
        # Add component info
        add_component_info,
        # Filter sensitive data
        filter_sensitive_data,
        # Add stack info for exceptions
        structlog.processors.StackInfoRenderer(),
        # Format exceptions
        structlog.dev.set_exc_info,
    ]

    # Add development-specific processors
    if is_development:
        processors.extend(
            [
                # Pretty print for development
                structlog.dev.ConsoleRenderer(colors=True),
            ]
        )
    else:
        processors.extend(
            [
                # JSON output for production
                structlog.processors.JSONRenderer(),
            ]
        )

    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )

    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level.upper(), logging.INFO),
    )


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a configured structlog logger.

    Args:
        name: Logger name (usually __name__)

    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


def set_request_context(
    request_id: Optional[str] = None,
    user_id: Optional[str] = None,
    operation: Optional[str] = None,
) -> None:
    """
    Set request context for logging.

    Args:
        request_id: Unique request identifier
        user_id: User identifier
        operation: Operation being performed
    """
    if request_id:
        request_id_context.set(request_id)
    if user_id:
        user_id_context.set(user_id)
    if operation:
        operation_context.set(operation)


def generate_request_id() -> str:
    """
    Generate a unique request ID.

    Returns:
        Unique request identifier
    """
    return str(uuid.uuid4())


def clear_request_context() -> None:
    """Clear all request context variables."""
    request_id_context.set(None)
    user_id_context.set(None)
    operation_context.set(None)


class RequestContextMiddleware:
    """Middleware to automatically set request context for each request."""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            # Generate request ID for HTTP requests
            request_id = generate_request_id()
            set_request_context(request_id=request_id)

            # Extract operation from path
            path = scope.get("path", "")
            if path.startswith("/api/"):
                operation = path.split("/")[-1] if "/" in path else "unknown"
                set_request_context(operation=operation)

        try:
            await self.app(scope, receive, send)
        finally:
            # Clear context after request
            clear_request_context()


# Initialize logging configuration
configure_structlog()

# Export commonly used functions
__all__ = [
    "get_logger",
    "set_request_context",
    "generate_request_id",
    "clear_request_context",
    "RequestContextMiddleware",
    "configure_structlog",
]
